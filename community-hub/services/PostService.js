/**
 * Post Service for Barber Brothers Community Hub
 * Handles all post-related operations including creation, retrieval, and interactions
 */

import AuthService from './AuthService.js';

class PostService {
    constructor() {
        this.db = null;
        this.storage = null;
        this.isInitialized = false;
        this.postsCache = new Map();
        this.listeners = new Map();
        
        this.init();
    }

    /**
     * Initialize the post service
     */
    async init() {
        try {
            // Wait for Firebase to be initialized
            await this.waitForFirebase();
            
            this.db = firebase.firestore();
            this.storage = firebase.storage();
            this.isInitialized = true;
            
            console.log('✅ PostService: Initialized successfully');
            
        } catch (error) {
            console.error('❌ PostService: Initialization failed:', error);
        }
    }

    /**
     * Wait for Firebase to be available
     */
    async waitForFirebase() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (typeof firebase !== 'undefined' && firebase.firestore) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);
        });
    }

    /**
     * Create a new post
     */
    async createPost(postData) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        const currentUser = AuthService.getCurrentUser();
        if (!currentUser) {
            throw new Error('User must be authenticated to create posts');
        }

        try {
            console.log('📝 PostService: Creating new post...');

            // Validate post data
            this.validatePostData(postData);

            // Process images if any
            const processedImages = await this.processImages(postData.images || []);

            // Create post object
            const post = {
                id: this.generatePostId(),
                authorId: currentUser.id,
                content: postData.content.trim(),
                images: processedImages,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                likes: [],
                comments: [],
                tags: this.extractTags(postData.content),
                mentions: this.extractMentions(postData.content),
                visibility: postData.visibility || 'public',
                location: postData.location || '',
                edited: false,
                editedAt: null
            };

            // Save to Firestore
            await this.db.collection('posts').doc(post.id).set(post);

            // Update user's post count
            await this.updateUserPostCount(currentUser.id, 1);

            // Clear cache to force refresh
            this.postsCache.clear();

            console.log('✅ PostService: Post created successfully:', post.id);
            return post;

        } catch (error) {
            console.error('❌ PostService: Failed to create post:', error);
            throw error;
        }
    }

    /**
     * Get posts for the feed
     */
    async getFeedPosts(options = {}) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        try {
            const {
                limit = 20,
                lastPost = null,
                userId = null,
                following = false
            } = options;

            console.log('📖 PostService: Loading feed posts...');

            let query = this.db.collection('posts');

            // Filter by user if specified
            if (userId) {
                query = query.where('authorId', '==', userId);
            }

            // Filter by following if specified
            if (following && AuthService.isAuthenticated()) {
                const currentUser = AuthService.getCurrentUser();
                const followingList = currentUser.following || [];
                
                if (followingList.length > 0) {
                    query = query.where('authorId', 'in', followingList);
                }
            }

            // Filter by visibility
            query = query.where('visibility', '==', 'public');

            // Order by timestamp
            query = query.orderBy('timestamp', 'desc');

            // Pagination
            if (lastPost) {
                query = query.startAfter(lastPost.timestamp);
            }

            // Limit results
            query = query.limit(limit);

            const snapshot = await query.get();
            const posts = [];

            for (const doc of snapshot.docs) {
                const postData = { id: doc.id, ...doc.data() };
                
                // Get author information
                const author = await AuthService.getUserById(postData.authorId);
                postData.author = author;

                posts.push(postData);
            }

            console.log(`✅ PostService: Loaded ${posts.length} posts`);
            return posts;

        } catch (error) {
            console.error('❌ PostService: Failed to load feed posts:', error);
            throw error;
        }
    }

    /**
     * Get a single post by ID
     */
    async getPostById(postId) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        try {
            // Check cache first
            if (this.postsCache.has(postId)) {
                return this.postsCache.get(postId);
            }

            const doc = await this.db.collection('posts').doc(postId).get();
            
            if (!doc.exists) {
                return null;
            }

            const postData = { id: doc.id, ...doc.data() };
            
            // Get author information
            const author = await AuthService.getUserById(postData.authorId);
            postData.author = author;

            // Cache the post
            this.postsCache.set(postId, postData);

            return postData;

        } catch (error) {
            console.error('❌ PostService: Failed to get post:', error);
            throw error;
        }
    }

    /**
     * Like or unlike a post
     */
    async toggleLike(postId) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        const currentUser = AuthService.getCurrentUser();
        if (!currentUser) {
            throw new Error('User must be authenticated to like posts');
        }

        try {
            const postRef = this.db.collection('posts').doc(postId);
            const post = await postRef.get();

            if (!post.exists) {
                throw new Error('Post not found');
            }

            const postData = post.data();
            const likes = postData.likes || [];
            const userIndex = likes.indexOf(currentUser.id);

            if (userIndex > -1) {
                // Unlike the post
                likes.splice(userIndex, 1);
                console.log('👎 PostService: Post unliked');
            } else {
                // Like the post
                likes.push(currentUser.id);
                console.log('👍 PostService: Post liked');
            }

            await postRef.update({ likes });

            // Clear cache
            this.postsCache.delete(postId);

            return { liked: userIndex === -1, likesCount: likes.length };

        } catch (error) {
            console.error('❌ PostService: Failed to toggle like:', error);
            throw error;
        }
    }

    /**
     * Delete a post
     */
    async deletePost(postId) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        const currentUser = AuthService.getCurrentUser();
        if (!currentUser) {
            throw new Error('User must be authenticated to delete posts');
        }

        try {
            const post = await this.getPostById(postId);
            
            if (!post) {
                throw new Error('Post not found');
            }

            // Check if user owns the post or is admin
            if (post.authorId !== currentUser.id && currentUser.role !== 'admin') {
                throw new Error('Not authorized to delete this post');
            }

            // Delete images from storage
            if (post.images && post.images.length > 0) {
                await this.deletePostImages(post.images);
            }

            // Delete post from Firestore
            await this.db.collection('posts').doc(postId).delete();

            // Update user's post count
            await this.updateUserPostCount(post.authorId, -1);

            // Clear cache
            this.postsCache.delete(postId);

            console.log('✅ PostService: Post deleted successfully');

        } catch (error) {
            console.error('❌ PostService: Failed to delete post:', error);
            throw error;
        }
    }

    /**
     * Process and upload images
     */
    async processImages(images) {
        if (!images || images.length === 0) {
            return [];
        }

        const processedImages = [];

        for (const image of images) {
            try {
                // Compress image if needed
                const compressedImage = await this.compressImage(image);
                
                // Upload to Firebase Storage
                const imageUrl = await this.uploadImage(compressedImage);
                
                processedImages.push({
                    url: imageUrl,
                    alt: image.alt || '',
                    width: image.width || null,
                    height: image.height || null
                });

            } catch (error) {
                console.error('❌ PostService: Failed to process image:', error);
                // Continue with other images
            }
        }

        return processedImages;
    }

    /**
     * Upload image to Firebase Storage
     */
    async uploadImage(imageFile) {
        const fileName = `posts/${Date.now()}_${Math.random().toString(36).substring(7)}`;
        const storageRef = this.storage.ref().child(fileName);
        
        const snapshot = await storageRef.put(imageFile);
        const downloadURL = await snapshot.ref.getDownloadURL();
        
        return downloadURL;
    }

    /**
     * Compress image for optimization
     */
    async compressImage(imageFile, maxWidth = 1200, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };

            img.src = URL.createObjectURL(imageFile);
        });
    }

    /**
     * Delete post images from storage
     */
    async deletePostImages(images) {
        for (const image of images) {
            try {
                const imageRef = this.storage.refFromURL(image.url);
                await imageRef.delete();
            } catch (error) {
                console.warn('⚠️ PostService: Could not delete image:', error);
            }
        }
    }

    /**
     * Extract hashtags from post content
     */
    extractTags(content) {
        const tagRegex = /#[\w]+/g;
        const matches = content.match(tagRegex);
        return matches ? matches.map(tag => tag.toLowerCase()) : [];
    }

    /**
     * Extract mentions from post content
     */
    extractMentions(content) {
        const mentionRegex = /@[\w]+/g;
        const matches = content.match(mentionRegex);
        return matches ? matches.map(mention => mention.substring(1).toLowerCase()) : [];
    }

    /**
     * Validate post data
     */
    validatePostData(postData) {
        if (!postData.content || postData.content.trim().length === 0) {
            throw new Error('Post content cannot be empty');
        }

        if (postData.content.length > 2000) {
            throw new Error('Post content is too long (max 2000 characters)');
        }

        if (postData.images && postData.images.length > 4) {
            throw new Error('Maximum 4 images allowed per post');
        }
    }

    /**
     * Generate unique post ID
     */
    generatePostId() {
        return `post_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }

    /**
     * Update user's post count
     */
    async updateUserPostCount(userId, increment) {
        try {
            const userRef = this.db.collection('users').doc(userId);
            await userRef.update({
                postsCount: firebase.firestore.FieldValue.increment(increment)
            });
        } catch (error) {
            console.warn('⚠️ PostService: Could not update post count:', error);
        }
    }

    /**
     * Listen for real-time post updates
     */
    listenToFeedUpdates(callback, options = {}) {
        if (!this.isInitialized) {
            throw new Error('PostService not initialized');
        }

        const listenerId = Math.random().toString(36).substring(7);

        let query = this.db.collection('posts')
            .where('visibility', '==', 'public')
            .orderBy('timestamp', 'desc')
            .limit(options.limit || 20);

        const unsubscribe = query.onSnapshot(async (snapshot) => {
            const posts = [];

            // Process posts sequentially to maintain order
            for (const doc of snapshot.docs) {
                const postData = { id: doc.id, ...doc.data() };

                // Get author information
                try {
                    const author = await AuthService.getUserById(postData.authorId);
                    postData.author = author;
                    posts.push(postData);
                } catch (error) {
                    console.warn('⚠️ PostService: Could not load author for post:', doc.id);
                }
            }

            callback(posts);
        });

        this.listeners.set(listenerId, unsubscribe);

        return () => {
            const listener = this.listeners.get(listenerId);
            if (listener) {
                listener();
                this.listeners.delete(listenerId);
            }
        };
    }

    /**
     * Cleanup
     */
    destroy() {
        // Unsubscribe from all listeners
        this.listeners.forEach(unsubscribe => unsubscribe());
        this.listeners.clear();
        
        // Clear cache
        this.postsCache.clear();
    }
}

// Export singleton instance
export default new PostService();
