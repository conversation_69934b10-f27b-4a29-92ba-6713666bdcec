<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Test - Barber Brothers Legacy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #dc3545;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #c82333;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #28a745; }
        .error { background: #dc3545; }
        .info { background: #17a2b8; }
        .warning { background: #ffc107; color: black; }
        .code {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔐 Google OAuth Test - Barber Brothers Legacy</h1>
    
    <div class="test-container">
        <h2>OAuth Configuration Test</h2>
        <p>This page tests the Google OAuth configuration for barberbrotherz.com</p>
        
        <div id="status-container"></div>
        
        <button class="btn" onclick="testConfiguration()">Test OAuth Configuration</button>
        <button class="btn" onclick="testGoogleSignIn()">Test Google Sign-In</button>
        <button class="btn" onclick="clearResults()">Clear Results</button>
        
        <div id="results-container"></div>
    </div>

    <div class="test-container">
        <h2>Current Configuration</h2>
        <div class="code" id="config-display">Loading configuration...</div>
    </div>

    <div class="test-container">
        <h2>Expected Google Cloud Console Settings</h2>
        <div class="code">
Client ID: 424859813197-45iu9t509loarues2v8vnmek3b3qi55b.apps.googleusercontent.com

Authorized JavaScript origins:
- https://www.barberbrotherz.com
- https://barberbrotherz.com
- http://localhost:3000
- http://127.0.0.1:3000

Authorized redirect URIs:
- https://www.barberbrotherz.com/auth-callback.html
- https://barberbrotherz.com/auth-callback.html
- http://localhost:3000/auth-callback.html
- http://127.0.0.1:3000/auth-callback.html
        </div>
    </div>

    <!-- Load OAuth configuration -->
    <script src="google-auth-config.js"></script>
    
    <script>
        function addStatus(type, message) {
            const container = document.getElementById('status-container');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(div);
        }

        function addResult(title, content) {
            const container = document.getElementById('results-container');
            const div = document.createElement('div');
            div.innerHTML = `
                <h3>${title}</h3>
                <div class="code">${content}</div>
            `;
            container.appendChild(div);
        }

        function clearResults() {
            document.getElementById('status-container').innerHTML = '';
            document.getElementById('results-container').innerHTML = '';
        }

        function testConfiguration() {
            clearResults();
            addStatus('info', 'Testing OAuth configuration...');

            try {
                // Test if configuration is loaded
                if (typeof GOOGLE_AUTH_CONFIG === 'undefined') {
                    throw new Error('GOOGLE_AUTH_CONFIG not loaded');
                }

                addStatus('success', 'OAuth configuration loaded successfully');

                // Display current configuration
                const config = {
                    'Client ID': GOOGLE_AUTH_CONFIG.clientId,
                    'Redirect URI': GOOGLE_AUTH_CONFIG.redirectUri,
                    'Scopes': GOOGLE_AUTH_CONFIG.scopes.join(', '),
                    'Response Type': GOOGLE_AUTH_CONFIG.responseType,
                    'Current Origin': window.location.origin,
                    'Current URL': window.location.href
                };

                addResult('Current Configuration', JSON.stringify(config, null, 2));

                // Test URL generation
                if (typeof getGoogleAuthUrl === 'function') {
                    const testUrl = getGoogleAuthUrl('test');
                    addStatus('success', 'OAuth URL generated successfully');
                    addResult('Generated OAuth URL', testUrl);

                    // Parse URL parameters
                    const url = new URL(testUrl);
                    const params = Object.fromEntries(url.searchParams);
                    addResult('OAuth Parameters', JSON.stringify(params, null, 2));
                } else {
                    throw new Error('getGoogleAuthUrl function not available');
                }

            } catch (error) {
                addStatus('error', `Configuration test failed: ${error.message}`);
                console.error('Configuration test error:', error);
            }
        }

        function testGoogleSignIn() {
            addStatus('info', 'Starting Google Sign-In test...');

            try {
                if (typeof getGoogleAuthUrl !== 'function') {
                    throw new Error('getGoogleAuthUrl function not available');
                }

                const authUrl = getGoogleAuthUrl('oauth-test');
                addStatus('success', 'Redirecting to Google Sign-In...');
                
                // Store test flag
                localStorage.setItem('oauthTest', 'true');
                localStorage.setItem('testTimestamp', Date.now().toString());

                // Redirect to Google
                setTimeout(() => {
                    window.location.href = authUrl;
                }, 1000);

            } catch (error) {
                addStatus('error', `Sign-in test failed: ${error.message}`);
                console.error('Sign-in test error:', error);
            }
        }

        // Display current configuration on page load
        document.addEventListener('DOMContentLoaded', function() {
            try {
                if (typeof GOOGLE_AUTH_CONFIG !== 'undefined') {
                    const configDisplay = document.getElementById('config-display');
                    configDisplay.textContent = JSON.stringify({
                        'Client ID': GOOGLE_AUTH_CONFIG.clientId,
                        'Redirect URI': GOOGLE_AUTH_CONFIG.redirectUri,
                        'Scopes': GOOGLE_AUTH_CONFIG.scopes,
                        'Current Origin': window.location.origin
                    }, null, 2);
                } else {
                    document.getElementById('config-display').textContent = 'ERROR: OAuth configuration not loaded';
                }

                // Check if returning from OAuth test
                if (localStorage.getItem('oauthTest') === 'true') {
                    addStatus('info', 'Returned from OAuth test');
                    localStorage.removeItem('oauthTest');
                    
                    // Check for auth success
                    if (localStorage.getItem('authSuccess') === 'true') {
                        addStatus('success', 'OAuth test completed successfully!');
                        localStorage.removeItem('authSuccess');
                    }
                }

            } catch (error) {
                console.error('Page load error:', error);
                addStatus('error', `Page load error: ${error.message}`);
            }
        });
    </script>
</body>
</html>
