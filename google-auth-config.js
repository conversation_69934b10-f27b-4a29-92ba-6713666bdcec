// Google OAuth Configuration
// This file contains the configuration for Google Sign-In

const GOOGLE_AUTH_CONFIG = {
    // Google OAuth Client ID for Barber Brothers Legacy
    // SECURITY UPDATE: Using secure client-side OAuth configuration
    clientId: '424859813197-47155f6nv5sjun0ekn0a7emftdesh35v.apps.googleusercontent.com',

    // Scopes to request from Google (minimal required scopes)
    scopes: [
        'openid',
        'email',
        'profile'
    ],

    // Redirect URI configuration for production domain
    get redirectUri() {
        const origin = window.location.origin;

        // Ensure we're using the correct production domain
        if (origin.includes('barberbrotherz.com')) {
            return 'https://www.barberbrotherz.com/auth-callback.html';
        }

        // For local development
        if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
            return origin + '/auth-callback.html';
        }

        // Default fallback
        return origin + '/auth-callback.html';
    },

    // SECURITY: Use authorization code flow (more secure)
    responseType: 'code',

    // Access type for refresh tokens
    accessType: 'online', // Changed from 'offline' for better security

    // Prompt parameter - force account selection for security
    prompt: 'select_account',

    // Additional security parameters
    include_granted_scopes: true,
    enable_granular_consent: true,

    // Security state parameter
    get state() {
        return {
            timestamp: Date.now(),
            nonce: Math.random().toString(36).substring(2, 15),
            origin: window.location.origin
        };
    }
};

// SECURITY UPDATE: Enhanced OAuth URL generation with better security
function getGoogleAuthUrl(source) {
    try {
        // Create secure state parameter with multiple security checks
        const stateData = {
            source: source,
            timestamp: Date.now(),
            nonce: Math.random().toString(36).substring(2, 15),
            origin: window.location.origin,
            // Add CSRF protection
            csrf: btoa(Math.random().toString()).substring(0, 12)
        };

        const state = encodeURIComponent(JSON.stringify(stateData));

        // Validate configuration before creating URL
        if (!GOOGLE_AUTH_CONFIG.clientId) {
            throw new Error('Google Client ID not configured');
        }

        const redirectUri = GOOGLE_AUTH_CONFIG.redirectUri;
        console.log('🔗 Using redirect URI:', redirectUri);

        // Build OAuth parameters with enhanced security
        const params = new URLSearchParams({
            client_id: GOOGLE_AUTH_CONFIG.clientId,
            redirect_uri: redirectUri,
            scope: GOOGLE_AUTH_CONFIG.scopes.join(' '),
            response_type: GOOGLE_AUTH_CONFIG.responseType,
            state: state,
            access_type: GOOGLE_AUTH_CONFIG.accessType,
            prompt: GOOGLE_AUTH_CONFIG.prompt,
            // Add additional security parameters
            include_granted_scopes: GOOGLE_AUTH_CONFIG.include_granted_scopes,
            enable_granular_consent: GOOGLE_AUTH_CONFIG.enable_granular_consent
        });

        const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;

        // Log for debugging (remove in production)
        console.log('🔐 Generated secure OAuth URL for source:', source);
        console.log('📋 OAuth parameters:', Object.fromEntries(params));

        return authUrl;

    } catch (error) {
        console.error('❌ Error generating OAuth URL:', error);
        throw new Error(`Failed to generate OAuth URL: ${error.message}`);
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GOOGLE_AUTH_CONFIG, getGoogleAuthUrl };
} else {
    window.GOOGLE_AUTH_CONFIG = GOOGLE_AUTH_CONFIG;
    window.getGoogleAuthUrl = getGoogleAuthUrl;
}
